import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def analyze_csv_files():
    """分析data文件夹中的5个CSV文件"""
    
    print("=" * 80)
    print("CSV文件数据分析报告")
    print("=" * 80)
    
    # 1. 分析nodes.csv
    print("\n1. NODES.CSV 分析")
    print("-" * 50)
    nodes = pd.read_csv('data/nodes.csv')
    print(f"数据形状: {nodes.shape}")
    print(f"列名: {list(nodes.columns)}")
    print(f"数据类型:\n{nodes.dtypes}")
    print(f"缺失值:\n{nodes.isnull().sum()}")
    print(f"经度范围: {nodes['long'].min():.6f} ~ {nodes['long'].max():.6f}")
    print(f"纬度范围: {nodes['lat'].min():.6f} ~ {nodes['lat'].max():.6f}")
    print(f"节点ID范围: {nodes['_id'].min()} ~ {nodes['_id'].max()}")
    
    # 2. 分析segments.csv
    print("\n2. SEGMENTS.CSV 分析")
    print("-" * 50)
    segments = pd.read_csv('data/segments.csv')
    print(f"数据形状: {segments.shape}")
    print(f"列名: {list(segments.columns)}")
    print(f"缺失值:\n{segments.isnull().sum()}")
    print(f"路段长度统计:\n{segments['length'].describe()}")
    print(f"最大速度统计:\n{segments['max_velocity'].describe()}")
    print(f"街道等级分布:\n{segments['street_level'].value_counts()}")
    print(f"街道类型分布:\n{segments['street_type'].value_counts()}")
    
    # 3. 分析streets.csv
    print("\n3. STREETS.CSV 分析")
    print("-" * 50)
    streets = pd.read_csv('data/streets.csv')
    print(f"数据形状: {streets.shape}")
    print(f"列名: {list(streets.columns)}")
    print(f"缺失值:\n{streets.isnull().sum()}")
    print(f"街道等级分布:\n{streets['level'].value_counts()}")
    print(f"街道类型分布:\n{streets['type'].value_counts()}")
    print(f"最大速度统计:\n{streets['max_velocity'].describe()}")
    
    # 4. 分析segment_status.csv
    print("\n4. SEGMENT_STATUS.CSV 分析")
    print("-" * 50)
    segment_status = pd.read_csv('data/segment_status.csv')
    print(f"数据形状: {segment_status.shape}")
    print(f"列名: {list(segment_status.columns)}")
    print(f"缺失值:\n{segment_status.isnull().sum()}")
    
    # 转换时间列
    segment_status['updated_at'] = pd.to_datetime(segment_status['updated_at'])
    print(f"时间范围: {segment_status['updated_at'].min()} ~ {segment_status['updated_at'].max()}")
    print(f"速度统计:\n{segment_status['velocity'].describe()}")
    print(f"唯一路段数: {segment_status['segment_id'].nunique()}")
    
    # 5. 分析train.csv
    print("\n5. TRAIN.CSV 分析")
    print("-" * 50)
    train = pd.read_csv('data/train.csv')
    print(f"数据形状: {train.shape}")
    print(f"列名: {list(train.columns)}")
    print(f"缺失值:\n{train.isnull().sum()}")
    
    # 转换日期列
    train['date'] = pd.to_datetime(train['date'])
    print(f"日期范围: {train['date'].min()} ~ {train['date'].max()}")
    print(f"星期分布:\n{train['weekday'].value_counts().sort_index()}")
    print(f"时间段分布:\n{train['period'].value_counts()}")
    print(f"LOS等级分布:\n{train['LOS'].value_counts()}")
    print(f"街道等级分布:\n{train['street_level'].value_counts()}")
    
    # 数据关系分析
    print("\n6. 数据关系分析")
    print("-" * 50)
    print(f"nodes表中的节点数: {len(nodes)}")
    print(f"segments表中引用的起始节点数: {segments['s_node_id'].nunique()}")
    print(f"segments表中引用的结束节点数: {segments['e_node_id'].nunique()}")
    print(f"segments表中的路段数: {len(segments)}")
    print(f"train表中引用的路段数: {train['segment_id'].nunique()}")
    print(f"segment_status表中引用的路段数: {segment_status['segment_id'].nunique()}")
    print(f"streets表中的街道数: {len(streets)}")
    print(f"segments表中引用的街道数: {segments['street_id'].nunique()}")
    
    return nodes, segments, streets, segment_status, train

# 可视化功能需要安装matplotlib库
# 如需可视化，请运行: pip install matplotlib seaborn

if __name__ == "__main__":
    # 执行分析
    nodes, segments, streets, segment_status, train = analyze_csv_files()

    print("\n" + "=" * 80)
    print("分析完成！")
    print("=" * 80)
